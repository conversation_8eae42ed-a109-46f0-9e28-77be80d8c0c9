import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/navigation_service.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/profile_sidebar.dart';
import '../products/product_details_screen.dart';

class CompareScreen extends ConsumerStatefulWidget {
  final String? initialProductName;
  final String? initialCompanyName;
  final String? initialPlan;
  final String? initialSumInsured;

  const CompareScreen({
    super.key,
    this.initialProductName,
    this.initialCompanyName,
    this.initialPlan,
    this.initialSumInsured,
  });

  @override
  ConsumerState<CompareScreen> createState() => _CompareScreenState();
}

class _CompareScreenState extends ConsumerState<CompareScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Fixed 3-slot comparison data structure - null represents empty slot
  final List<Map<String, dynamic>?> _comparisonSlots = [
    {
      'company': 'SecureHealth',
      'product': 'Health Insurance',
      'plan': 'Premium Plan',
      'sumInsured': '₹50,00,000',
      'premium': '₹15,000',
      'features': [
        'Hospitalization Coverage',
        'Outpatient Care',
        'Prescription Drugs',
        'Emergency Care',
        'Maternity Benefits',
      ],
      'rating': 4.5,
      'color': Color(0xFF086788),
    },
    {
      'company': 'LifeGuard',
      'product': 'Health Plus',
      'plan': 'Gold Plan',
      'sumInsured': '₹50,00,000',
      'premium': '₹18,000',
      'features': [
        'Hospitalization Coverage',
        'Outpatient Care',
        'Dental Care',
        'Emergency Care',
        'Wellness Programs',
      ],
      'rating': 4.2,
      'color': Color(0xFF157A6E),
    },
    {
      'company': 'TravelSafe',
      'product': 'Health Shield',
      'plan': 'Platinum Plan',
      'sumInsured': '₹50,00,000',
      'premium': '₹22,000',
      'features': [
        'Hospitalization Coverage',
        'Outpatient Care',
        'Prescription Drugs',
        'Emergency Care',
        'International Coverage',
      ],
      'rating': 4.7,
      'color': Color(0xFF306B34),
    },
  ];

  // Get only non-null products for comparison table
  List<Map<String, dynamic>> get _comparisonProducts =>
      _comparisonSlots.where((slot) => slot != null).cast<Map<String, dynamic>>().toList();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xFFf1f1f1),
      drawer: Drawer(
        child: Consumer(
          builder: (context, ref, child) {
            final currentUser = ref.watch(currentUserProvider);
            return ProfileSidebar(
              userName: currentUser?.displayName ?? 'Name',
              userEmail: currentUser?.email ?? '<EMAIL>',
              userPhone: currentUser?.phoneNumber ?? '+91 - - - - - - - - - -',
              profileImageUrl: currentUser?.photoURL,
              onLogout: () {
                Navigator.pop(context); // Close drawer
                ref.read(authProvider.notifier).signOut();
                NavigationService.instance.navigateToAlternateSignIn();
              },
              onEditProfile: () {
                Navigator.pop(context); // Close drawer
                NavigationService.instance.navigateToProfile();
              },
            );
          },
        ),
      ),
      body: Column(
        children: [
          // Header
          Container(
            color: const Color(0xFFf1f1f1),
            child: SafeArea(
              bottom: false,
              child: Container(
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(
                  color: Color(0xFFf1f1f1),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x0F000000),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    const Expanded(
                      child: Text(
                        'Compare Plans',
                        style: TextStyle(
                          color: Color(0xFF111418),
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    // Home icon
                    IconButton(
                      onPressed: () {
                        NavigationService.instance.navigateToAlternateHome();
                      },
                      icon: const Icon(
                        Icons.home,
                        color: Color(0xFFe92933),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Body content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Comparison cards
                  _buildComparisonCards(),
                  const SizedBox(height: 24),

                  // Feature comparison table
                  _buildFeatureComparisonTable(),
                  const SizedBox(height: 24),

                  // Action buttons
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildComparisonCards() {
    return SizedBox(
      height: 200, // Increased height to prevent overflow
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 3, // Always show exactly 3 slots
        itemBuilder: (context, index) {
          final slot = _comparisonSlots[index];
          return Container(
            width: 260, // Increased width to prevent overflow
            margin: EdgeInsets.only(right: index < 2 ? 16 : 0), // Right margin for first 2 cards only
            child: slot != null
                ? _buildProductCard(slot, index)
                : _buildPlaceholderCard(index),
          );
        },
      ),
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product, int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 32, 16, 16), // Top padding for red cross, normal bottom padding
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min, // Take only the space needed
                    children: [
                    // Company and product details
                    _buildDetailRow('Company', product['company']),
                    const SizedBox(height: 2),
                    _buildDetailRow('Product', product['product']),
                    const SizedBox(height: 2),
                    _buildDetailRow('Plan', product['plan']),
                    const SizedBox(height: 2),
                    _buildDetailRow('Sum Insured', product['sumInsured']),
                    const SizedBox(height: 8),

                    // Rating with edit icon
                    Row(
                      children: [
                        ...List.generate(5, (starIndex) {
                          return Icon(
                            starIndex < product['rating'].floor()
                              ? Icons.star
                              : starIndex < product['rating']
                                ? Icons.star_half
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 16,
                          );
                        }),
                        const SizedBox(width: 8),
                        Text(
                          '${product['rating']}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF111418),
                          ),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: () {
                            _showEditProductDialog(product, index);
                          },
                          child: const Icon(
                            Icons.edit,
                            color: Color(0xFFe92933),
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                ),
              ),
            ],
          ),

          // Red cross at top right corner
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                _showRemoveCardDialog(product, index);
              },
              child: const Icon(
                Icons.close,
                color: Color(0xFFe92933),
                size: 20,
              ),
            ),
          ),


        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF637488),
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
            textAlign: TextAlign.end,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholderCard(int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFe92933),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showAddProductDialog(index),
          borderRadius: BorderRadius.circular(12),
          child: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: CustomPaint(
              painter: DottedBorderPainter(
                color: const Color(0xFFe92933),
                strokeWidth: 2,
                dashLength: 8,
                gapLength: 4,
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add,
                      size: 32,
                      color: Color(0xFFe92933),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Add Product',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFFe92933),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureComparisonTable() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFFe92933),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Text(
              'Feature Comparison',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),

          // Table content
          _buildComparisonTable(),
        ],
      ),
    );
  }

  Widget _buildComparisonTable() {
    // Get all unique features
    final allFeatures = <String>{};
    for (final product in _comparisonProducts) {
      allFeatures.addAll(List<String>.from(product['features']));
    }
    final featuresList = allFeatures.toList();

    return Table(
      border: TableBorder(
        horizontalInside: const BorderSide(color: Color(0xFFe0e0e0), width: 1),
        verticalInside: const BorderSide(color: Color(0xFFe0e0e0), width: 1),
      ),
      children: [
        // Header row
        TableRow(
          decoration: const BoxDecoration(color: Color(0xFFf8f9fa)),
          children: [
            _buildTableHeader('Features'),
            ..._comparisonProducts.map((product) =>
              _buildTableHeader(product['company'])),
          ],
        ),
        // Feature rows
        ...featuresList.map((feature) => TableRow(
          children: [
            _buildTableCell(feature),
            ..._comparisonProducts.map((product) {
              final hasFeature = List<String>.from(product['features']).contains(feature);
              return _buildFeatureCell(hasFeature);
            }),
          ],
        )),
      ],
    );
  }

  Widget _buildTableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Color(0xFF111418),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTableCell(String text) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF111418),
        ),
      ),
    );
  }

  Widget _buildFeatureCell(bool hasFeature) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Center(
        child: Icon(
          hasFeature ? Icons.check_circle : Icons.cancel,
          color: hasFeature ? Colors.green : Colors.red,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                _showAddMorePlansDialog();
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFFe92933),
                side: const BorderSide(color: Color(0xFFe92933)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Add More Plans',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                _showSaveComparisonDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Save Comparison',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showRemoveCardDialog(Map<String, dynamic> product, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'Remove from Comparison',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          content: Text(
            'Are you sure you want to remove ${product['product']} from ${product['company']} from the comparison?',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Color(0xFF637488)),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                setState(() {
                  _comparisonSlots[index] = null; // Set slot to null instead of removing
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${product['product']} removed from comparison'),
                    backgroundColor: const Color(0xFFe92933),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
              ),
              child: const Text('Remove'),
            ),
          ],
        );
      },
    );
  }

  void _showEditProductDialog(Map<String, dynamic> product, int index) {
    showDialog(
      context: context,
      builder: (context) => EditProductDialog(
        currentCompany: product['company'],
        currentProduct: product['product'],
        currentPlan: product['plan'],
        currentSumInsured: product['sumInsured'],
        insuranceData: _getInsuranceData(),
        onUpdate: (company, productName, plan, sumInsured) {
          setState(() {
            _comparisonSlots[index] = {
              ..._comparisonSlots[index]!,
              'company': company,
              'product': productName,
              'plan': plan,
              'sumInsured': sumInsured,
            };
          });
        },
      ),
    );
  }

  Map<String, Map<String, dynamic>> _getInsuranceData() {
    // Sample insurance data structure - in real app this would come from API
    return {
      'SecureHealth': {
        'products': {
          'Health Insurance': {
            'plans': {
              'Basic Plan': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Premium Plan': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Platinum Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Life Insurance': {
            'plans': {
              'Term Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
              'Whole Life': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
            }
          }
        }
      },
      'LifeGuard': {
        'products': {
          'Health Plus': {
            'plans': {
              'Silver Plan': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Gold Plan': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Diamond Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Motor Insurance': {
            'plans': {
              'Third Party': ['₹5,00,000', '₹10,00,000'],
              'Comprehensive': ['₹10,00,000', '₹25,00,000', '₹50,00,000'],
            }
          }
        }
      },
      'TravelSafe': {
        'products': {
          'Health Shield': {
            'plans': {
              'Basic Shield': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Premium Shield': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Platinum Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Travel Insurance': {
            'plans': {
              'Domestic': ['₹1,00,000', '₹5,00,000'],
              'International': ['₹10,00,000', '₹25,00,000', '₹50,00,000'],
            }
          }
        }
      }
    };
  }

  void _showAddMorePlansDialog() {
    // Find the first empty slot
    final emptySlotIndex = _comparisonSlots.indexWhere((slot) => slot == null);

    if (emptySlotIndex == -1) {
      // All slots are filled
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('All comparison slots are filled. Remove a product to add a new one.'),
          backgroundColor: Color(0xFFe92933),
        ),
      );
      return;
    }

    // Open the add product dialog for the empty slot
    _showAddProductDialog(emptySlotIndex);
  }

  void _showSaveComparisonDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'Save Comparison',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          content: const Text(
            'Save this comparison to access it later from your saved comparisons.',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Color(0xFF637488)),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Comparison saved successfully!'),
                    backgroundColor: Color(0xFFe92933),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
              ),
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  void _showAddProductDialog(int slotIndex) {
    showDialog(
      context: context,
      builder: (context) => EditProductDialog(
        currentCompany: 'SecureHealth', // Default company
        currentProduct: 'Health Insurance', // Default product
        currentPlan: 'Basic Plan', // Default plan
        currentSumInsured: '₹5,00,000', // Default sum insured
        insuranceData: _getInsuranceData(),
        onUpdate: (company, productName, plan, sumInsured) {
          setState(() {
            _comparisonSlots[slotIndex] = {
              'company': company,
              'product': productName,
              'plan': plan,
              'sumInsured': sumInsured,
              'premium': '₹15,000', // Default premium - in real app would be calculated
              'features': [
                'Hospitalization Coverage',
                'Outpatient Care',
                'Prescription Drugs',
                'Emergency Care',
              ],
              'rating': 4.0, // Default rating
              'color': const Color(0xFF086788), // Default color
            };
          });
        },
      ),
    );
  }
}

// Custom painter for dotted border
class DottedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double gapLength;

  DottedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.gapLength,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(strokeWidth / 2, strokeWidth / 2,
                     size.width - strokeWidth, size.height - strokeWidth),
        const Radius.circular(12),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final segment = pathMetric.extractPath(
          distance,
          distance + dashLength,
        );
        canvas.drawPath(segment, paint);
        distance += dashLength + gapLength;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
