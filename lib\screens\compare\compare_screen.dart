import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/navigation_service.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/profile_sidebar.dart';
import '../products/product_details_screen.dart';

class CompareScreen extends ConsumerStatefulWidget {
  final String? initialProductName;
  final String? initialCompanyName;
  final String? initialPlan;
  final String? initialSumInsured;

  const CompareScreen({
    super.key,
    this.initialProductName,
    this.initialCompanyName,
    this.initialPlan,
    this.initialSumInsured,
  });

  @override
  ConsumerState<CompareScreen> createState() => _CompareScreenState();
}

class _CompareScreenState extends ConsumerState<CompareScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int _selectedTabIndex = 0; // 0 for Benefits, 1 for Premiums

  // Fixed 3-slot comparison data structure - null represents empty slot
  final List<Map<String, dynamic>?> _comparisonSlots = [
    {
      'company': 'SecureHealth',
      'product': 'Health Insurance',
      'plan': 'Premium Plan',
      'sumInsured': '₹50,00,000',
      'premium': '₹15,000',
      'features': [
        'Hospitalization Coverage',
        'Outpatient Care',
        'Prescription Drugs',
        'Emergency Care',
        'Maternity Benefits',
      ],
      'rating': 4.5,
      'color': Color(0xFF086788),
    },
    {
      'company': 'LifeGuard',
      'product': 'Health Plus',
      'plan': 'Gold Plan',
      'sumInsured': '₹50,00,000',
      'premium': '₹18,000',
      'features': [
        'Hospitalization Coverage',
        'Outpatient Care',
        'Dental Care',
        'Emergency Care',
        'Wellness Programs',
      ],
      'rating': 4.2,
      'color': Color(0xFF157A6E),
    },
    {
      'company': 'TravelSafe',
      'product': 'Health Shield',
      'plan': 'Platinum Plan',
      'sumInsured': '₹50,00,000',
      'premium': '₹22,000',
      'features': [
        'Hospitalization Coverage',
        'Outpatient Care',
        'Prescription Drugs',
        'Emergency Care',
        'International Coverage',
      ],
      'rating': 4.7,
      'color': Color(0xFF306B34),
    },
  ];

  // Get only non-null products for comparison table
  List<Map<String, dynamic>> get _comparisonProducts =>
      _comparisonSlots.where((slot) => slot != null).cast<Map<String, dynamic>>().toList();

  // Helper method to shift cards left and keep placeholders on the right
  void _reorganizeSlots() {
    final nonNullSlots = <Map<String, dynamic>>[];

    // Collect all non-null slots
    for (final slot in _comparisonSlots) {
      if (slot != null) {
        nonNullSlots.add(slot);
      }
    }

    // Reorganize: non-null slots first, then null slots
    for (int i = 0; i < 3; i++) {
      if (i < nonNullSlots.length) {
        _comparisonSlots[i] = nonNullSlots[i];
      } else {
        _comparisonSlots[i] = null;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xFFf1f1f1),
      drawer: Drawer(
        child: Consumer(
          builder: (context, ref, child) {
            final currentUser = ref.watch(currentUserProvider);
            return ProfileSidebar(
              userName: currentUser?.displayName ?? 'Name',
              userEmail: currentUser?.email ?? '<EMAIL>',
              userPhone: currentUser?.phoneNumber ?? '+91 - - - - - - - - - -',
              profileImageUrl: currentUser?.photoURL,
              onLogout: () {
                Navigator.pop(context); // Close drawer
                ref.read(authProvider.notifier).signOut();
                NavigationService.instance.navigateToAlternateSignIn();
              },
              onEditProfile: () {
                Navigator.pop(context); // Close drawer
                NavigationService.instance.navigateToProfile();
              },
            );
          },
        ),
      ),
      body: Column(
        children: [
          // Header
          Container(
            color: const Color(0xFFf1f1f1),
            child: SafeArea(
              bottom: false,
              child: Container(
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(
                  color: Color(0xFFf1f1f1),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x0F000000),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    const Expanded(
                      child: Text(
                        'Compare Plans',
                        style: TextStyle(
                          color: Color(0xFF111418),
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    // Home icon
                    IconButton(
                      onPressed: () {
                        NavigationService.instance.navigateToAlternateHome();
                      },
                      icon: const Icon(
                        Icons.home,
                        color: Color(0xFFe92933),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Body content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Comparison cards
                  _buildComparisonCards(),
                  const SizedBox(height: 24),

                  // Feature comparison table
                  _buildFeatureComparisonTable(),
                  const SizedBox(height: 24),

                  // Action buttons
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildComparisonCards() {
    return SizedBox(
      height: 160, // Increased height to prevent overflow
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 3, // Always show exactly 3 slots
        itemBuilder: (context, index) {
          final slot = _comparisonSlots[index];
          return Container(
            width: 260, // Set to exactly 260px as requested
            margin: EdgeInsets.only(right: index < 2 ? 16 : 0), // Right margin for first 2 cards only
            child: slot != null
                ? _buildProductCard(slot, index)
                : _buildPlaceholderCard(index),
          );
        },
      ),
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product, int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 32, 16, 16), // Top padding for red cross, normal bottom padding
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min, // Take only the space needed
                    children: [
                    // Company and product details
                    _buildDetailRow('Company', product['company']),
                    const SizedBox(height: 2),
                    _buildDetailRow('Product', product['product']),
                    const SizedBox(height: 2),
                    _buildDetailRow('Plan', product['plan']),
                    const SizedBox(height: 2),
                    _buildDetailRow('Sum Insured', product['sumInsured']),
                    const SizedBox(height: 8),

                    // Rating with edit icon
                    Row(
                      children: [
                        ...List.generate(5, (starIndex) {
                          return Icon(
                            starIndex < product['rating'].floor()
                              ? Icons.star
                              : starIndex < product['rating']
                                ? Icons.star_half
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 16,
                          );
                        }),
                        const SizedBox(width: 8),
                        Text(
                          '${product['rating']}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF111418),
                          ),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: () {
                            _showEditProductDialog(product, index);
                          },
                          child: const Icon(
                            Icons.edit,
                            color: Color(0xFFe92933),
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                ),
              ),
            ],
          ),

          // Red cross at top right corner
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                _removeCard(index);
              },
              child: const Icon(
                Icons.close,
                color: Color(0xFFe92933),
                size: 20,
              ),
            ),
          ),


        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF637488),
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
            textAlign: TextAlign.end,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholderCard(int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showAddProductDialog(index),
          borderRadius: BorderRadius.circular(12),
          child: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: CustomPaint(
              painter: DottedBorderPainter(
                color: const Color(0xFFe92933),
                strokeWidth: 2,
                dashLength: 8,
                gapLength: 4,
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add,
                      size: 32,
                      color: Color(0xFFe92933),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Add Product',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFFe92933),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureComparisonTable() {
    return Column(
      children: [
        // Tab Navigation Section - separate from table
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: _buildTabNavigation(),
        ),

        // Table Content Section - separate container
        Container(
          margin: const EdgeInsets.fromLTRB(16, 12, 16, 0), // Added 12px top margin for spacing between tabs and table
          decoration: BoxDecoration(
            color: const Color(0xFFf1f1f1), // Background color matching product details
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _selectedTabIndex == 0
              ? _buildBenefitsTable()
              : _buildPremiumsTable(),
        ),
      ],
    );
  }

  Widget _buildTabNavigation() {
    return Column(
      children: [
        // Tabs Section - matching product details page design exactly
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              const SizedBox(width: 16), // Left padding
              _buildTab('Benefits', 0),
              _buildTab('Premiums', 1),
              const SizedBox(width: 16), // Right padding
            ],
          ),
        ),
        // Running line below all tabs (edge-to-edge)
        Container(
          height: 1,
          width: double.infinity,
          color: const Color(0xFFE0E0E0),
        ),
      ],
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    // Dynamic width based on text length to prevent overflow
    double tabWidth = 100; // Default width for Benefits/Premiums
    if (title == 'Premiums') {
      tabWidth = 100; // Same width for consistency
    }

    return GestureDetector(
      onTap: () => setState(() => _selectedTabIndex = index),
      child: Container(
        width: tabWidth,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFe92933) : Colors.transparent,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
            bottomLeft: Radius.circular(0),
            bottomRight: Radius.circular(0),
          ),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: isSelected ? Colors.white : const Color(0xFF086788),
          ),
        ),
      ),
    );
  }

  Widget _buildBenefitsTable() {
    final benefitsData = _getBenefitsData();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          border: TableBorder.all(
            color: const Color(0xFFe0e0e0),
            width: 1,
          ),
          columnSpacing: 0, // Remove spacing to control width precisely
          headingRowHeight: 80, // Increased for logo + text layout
          horizontalMargin: 0, // Remove default horizontal margin
          columns: [
            DataColumn(
              label: SizedBox(
                width: 135, // Reduced width: 135px for benefit names (25% reduction from 180px)
                child: Container(
                  alignment: Alignment.centerLeft,
                  padding: const EdgeInsets.only(left: 12.0, right: 4.0, top: 4.0, bottom: 4.0),
                  child: const Text(
                    'Benefits',
                    style: TextStyle(
                      fontSize: 12, // Consistent font size with other columns
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF111418),
                    ),
                    softWrap: true,
                  ),
                ),
              ),
            ),
            ..._comparisonProducts.map((product) => DataColumn(
              label: SizedBox(
                width: 200, // Fixed width: 200px for company descriptions
                child: Container(
                  padding: const EdgeInsets.only(left: 4.0, right: 12.0, top: 4.0, bottom: 4.0),
                  child: Row(
                    children: [
                      // Left Section (40% of column width) - Company Logo
                      Expanded(
                        flex: 40,
                        child: Container(
                          height: 50,
                          padding: const EdgeInsets.all(4.0),
                          child: _buildCompanyLogo(product['company']),
                        ),
                      ),
                      // Right Section (60% of column width) - Product Details
                      Expanded(
                        flex: 60,
                        child: Container(
                          padding: const EdgeInsets.only(left: 4.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                product['product'],
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF111418),
                                ),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                product['plan'],
                                style: const TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF637488),
                                ),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                product['sumInsured'],
                                style: const TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF086788),
                                ),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )),
          ],
          rows: benefitsData.keys.map((benefit) => DataRow(
            cells: [
              DataCell(
                SizedBox(
                  width: 135, // Reduced width: 135px for benefit names
                  child: Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(left: 12.0, right: 4.0, top: 10.0, bottom: 10.0),
                    child: Text(
                      benefit,
                      style: const TextStyle(
                        fontSize: 12, // Consistent font size with other columns
                        color: Color(0xFF111418),
                      ),
                      softWrap: true, // Allow text wrapping
                    ),
                  ),
                ),
              ),
              ..._comparisonProducts.map((product) => DataCell(
                SizedBox(
                  width: 200, // Fixed width: 200px for company descriptions
                  child: Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(left: 4.0, right: 12.0, top: 10.0, bottom: 10.0),
                    child: Text(
                      benefitsData[benefit]?[product['company']] ?? 'Not available',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF111418),
                      ),
                      softWrap: true, // Allow text wrapping
                    ),
                  ),
                ),
              )),
            ],
          )).toList(),
        ),
      ),
    );
  }

  Widget _buildCompanyLogo(String companyName) {
    // Company logo mapping - in real app, these would be actual logo assets
    final logoMap = {
      'SecureHealth': Icons.local_hospital,
      'LifeGuard': Icons.favorite,
      'TravelSafe': Icons.flight,
    };

    final logoIcon = logoMap[companyName] ?? Icons.business;
    final logoColor = _getCompanyColor(companyName);

    return Container(
      decoration: BoxDecoration(
        color: logoColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: logoColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Icon(
          logoIcon,
          color: logoColor,
          size: 24,
        ),
      ),
    );
  }

  Color _getCompanyColor(String companyName) {
    switch (companyName) {
      case 'SecureHealth':
        return const Color(0xFF086788);
      case 'LifeGuard':
        return const Color(0xFF157A6E);
      case 'TravelSafe':
        return const Color(0xFF306B34);
      default:
        return const Color(0xFF637488);
    }
  }

  Widget _buildPremiumsTable() {
    final premiumsData = _getPremiumsData();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          border: TableBorder.all(
            color: const Color(0xFFe0e0e0),
            width: 1,
          ),
          columnSpacing: 0, // Remove spacing to control width precisely
          headingRowHeight: 80, // Increased for logo + text layout
          horizontalMargin: 0, // Remove default horizontal margin
          columns: [
            DataColumn(
              label: SizedBox(
                width: 135, // Reduced width: 135px for premium details (25% reduction from 180px)
                child: Container(
                  alignment: Alignment.centerLeft,
                  padding: const EdgeInsets.only(left: 12.0, right: 4.0, top: 4.0, bottom: 4.0),
                  child: const Text(
                    'Premium Details',
                    style: TextStyle(
                      fontSize: 12, // Consistent font size with other columns
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF111418),
                    ),
                    softWrap: true,
                  ),
                ),
              ),
            ),
            ..._comparisonProducts.map((product) => DataColumn(
              label: SizedBox(
                width: 200, // Fixed width: 200px for company descriptions
                child: Container(
                  padding: const EdgeInsets.only(left: 4.0, right: 12.0, top: 4.0, bottom: 4.0),
                  child: Row(
                    children: [
                      // Left Section (40% of column width) - Company Logo
                      Expanded(
                        flex: 40,
                        child: Container(
                          height: 50,
                          padding: const EdgeInsets.all(4.0),
                          child: _buildCompanyLogo(product['company']),
                        ),
                      ),
                      // Right Section (60% of column width) - Product Details
                      Expanded(
                        flex: 60,
                        child: Container(
                          padding: const EdgeInsets.only(left: 4.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                product['product'],
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF111418),
                                ),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                product['plan'],
                                style: const TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF637488),
                                ),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                product['sumInsured'],
                                style: const TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF086788),
                                ),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )),
          ],
          rows: premiumsData.keys.map((premiumType) => DataRow(
            cells: [
              DataCell(
                SizedBox(
                  width: 135, // Reduced width: 135px for premium details
                  child: Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(left: 12.0, right: 4.0, top: 10.0, bottom: 10.0),
                    child: Text(
                      premiumType,
                      style: const TextStyle(
                        fontSize: 12, // Consistent font size with other columns
                        color: Color(0xFF111418),
                      ),
                      softWrap: true, // Allow text wrapping
                    ),
                  ),
                ),
              ),
              ..._comparisonProducts.map((product) => DataCell(
                SizedBox(
                  width: 200, // Fixed width: 200px for company descriptions
                  child: Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(left: 4.0, right: 12.0, top: 10.0, bottom: 10.0),
                    child: Text(
                      premiumsData[premiumType]?[product['company']] ?? 'Not available',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF111418),
                      ),
                      softWrap: true, // Allow text wrapping
                    ),
                  ),
                ),
              )),
            ],
          )).toList(),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            _showSaveComparisonDialog();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFe92933),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text(
            'Save Comparison',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  void _removeCard(int index) {
    final product = _comparisonSlots[index];
    if (product != null) {
      setState(() {
        _comparisonSlots[index] = null;
        _reorganizeSlots(); // Automatically shift cards left
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product['product']} removed from comparison'),
          backgroundColor: const Color(0xFFe92933),
        ),
      );
    }
  }

  void _showEditProductDialog(Map<String, dynamic> product, int index) {
    showDialog(
      context: context,
      builder: (context) => EditProductDialog(
        currentCompany: product['company'],
        currentProduct: product['product'],
        currentPlan: product['plan'],
        currentSumInsured: product['sumInsured'],
        insuranceData: _getInsuranceData(),
        onUpdate: (company, productName, plan, sumInsured) {
          setState(() {
            _comparisonSlots[index] = {
              ..._comparisonSlots[index]!,
              'company': company,
              'product': productName,
              'plan': plan,
              'sumInsured': sumInsured,
            };
          });
        },
      ),
    );
  }

  Map<String, Map<String, String>> _getBenefitsData() {
    // Sample benefits data with detailed descriptions
    return {
      'Hospitalization Coverage': {
        'SecureHealth': 'Comprehensive coverage for room rent up to ₹10,000/day, ICU charges, surgeon fees, and all medical expenses during hospitalization',
        'LifeGuard': 'Full hospitalization coverage including private room, specialist consultations, diagnostic tests, and emergency procedures',
        'TravelSafe': 'Complete inpatient care coverage with cashless facility at 5000+ network hospitals across India and abroad',
      },
      'Outpatient Care': {
        'SecureHealth': 'OPD consultations, diagnostic tests, pharmacy bills up to ₹25,000 annually with 50% co-payment',
        'LifeGuard': 'Unlimited OPD visits, preventive health checkups, dental and eye care with no co-payment required',
        'TravelSafe': 'Comprehensive outpatient coverage including specialist consultations, lab tests, and prescription medicines',
      },
      'Prescription Drugs': {
        'SecureHealth': 'Medicine coverage up to ₹15,000 per year for chronic conditions and post-hospitalization care',
        'LifeGuard': 'Full prescription drug coverage with home delivery service and generic medicine options',
        'TravelSafe': 'Prescription medicine coverage including imported drugs and specialized treatments up to policy limit',
      },
      'Emergency Care': {
        'SecureHealth': '24/7 emergency services, ambulance coverage up to ₹2,000 per incident, and emergency room visits',
        'LifeGuard': 'Comprehensive emergency care with air ambulance facility, trauma care, and critical care coverage',
        'TravelSafe': 'Global emergency assistance, medical evacuation, and emergency treatment coverage worldwide',
      },
      'Maternity Benefits': {
        'SecureHealth': 'Maternity coverage up to ₹50,000 including normal delivery, C-section, and newborn care for 30 days',
        'LifeGuard': 'Not available',
        'TravelSafe': 'Not available',
      },
      'Dental Care': {
        'SecureHealth': 'Not available',
        'LifeGuard': 'Complete dental care including routine checkups, cleanings, fillings, and major dental procedures',
        'TravelSafe': 'Not available',
      },
      'Wellness Programs': {
        'SecureHealth': 'Not available',
        'LifeGuard': 'Annual health checkups, fitness programs, nutrition counseling, and wellness coaching',
        'TravelSafe': 'Not available',
      },
      'International Coverage': {
        'SecureHealth': 'Not available',
        'LifeGuard': 'Not available',
        'TravelSafe': 'Worldwide coverage including medical treatment, emergency evacuation, and repatriation services',
      },
    };
  }

  Map<String, Map<String, String>> _getPremiumsData() {
    // Sample premiums data with detailed breakdown
    return {
      'Annual Premium': {
        'SecureHealth': '₹15,000 (Base premium for selected coverage)',
        'LifeGuard': '₹18,000 (Includes additional wellness benefits)',
        'TravelSafe': '₹22,000 (Premium coverage with international benefits)',
      },
      'Monthly Premium': {
        'SecureHealth': '₹1,250 (EMI option available with 0% interest)',
        'LifeGuard': '₹1,500 (Flexible payment options available)',
        'TravelSafe': '₹1,833 (Premium monthly plan with global coverage)',
      },
      'Deductible': {
        'SecureHealth': '₹5,000 per claim (Applicable for all claims)',
        'LifeGuard': '₹3,000 per claim (Reduced deductible for network hospitals)',
        'TravelSafe': '₹10,000 per claim (Higher deductible for comprehensive coverage)',
      },
      'Co-payment': {
        'SecureHealth': '10% for all claims (Minimum ₹1,000 per claim)',
        'LifeGuard': '5% for network hospitals, 15% for non-network',
        'TravelSafe': 'No co-payment for cashless treatment at network hospitals',
      },
      'Room Rent Limit': {
        'SecureHealth': '1% of sum insured per day (Maximum ₹10,000/day)',
        'LifeGuard': '2% of sum insured per day (Maximum ₹15,000/day)',
        'TravelSafe': 'No room rent limit (Any room type allowed)',
      },
      'Waiting Period': {
        'SecureHealth': '2 years for pre-existing diseases, 4 years for specific conditions',
        'LifeGuard': '3 years for pre-existing diseases, immediate coverage for accidents',
        'TravelSafe': '1 year for pre-existing diseases, immediate emergency coverage',
      },
    };
  }

  Map<String, Map<String, dynamic>> _getInsuranceData() {
    // Sample insurance data structure - in real app this would come from API
    return {
      'SecureHealth': {
        'products': {
          'Health Insurance': {
            'plans': {
              'Basic Plan': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Premium Plan': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Platinum Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Life Insurance': {
            'plans': {
              'Term Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
              'Whole Life': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
            }
          }
        }
      },
      'LifeGuard': {
        'products': {
          'Health Plus': {
            'plans': {
              'Silver Plan': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Gold Plan': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Diamond Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Motor Insurance': {
            'plans': {
              'Third Party': ['₹5,00,000', '₹10,00,000'],
              'Comprehensive': ['₹10,00,000', '₹25,00,000', '₹50,00,000'],
            }
          }
        }
      },
      'TravelSafe': {
        'products': {
          'Health Shield': {
            'plans': {
              'Basic Shield': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Premium Shield': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Platinum Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Travel Insurance': {
            'plans': {
              'Domestic': ['₹1,00,000', '₹5,00,000'],
              'International': ['₹10,00,000', '₹25,00,000', '₹50,00,000'],
            }
          }
        }
      }
    };
  }

  void _showAddMorePlansDialog() {
    // Find the first empty slot
    final emptySlotIndex = _comparisonSlots.indexWhere((slot) => slot == null);

    if (emptySlotIndex == -1) {
      // All slots are filled
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('All comparison slots are filled. Remove a product to add a new one.'),
          backgroundColor: Color(0xFFe92933),
        ),
      );
      return;
    }

    // Open the add product dialog for the empty slot
    _showAddProductDialog(emptySlotIndex);
  }

  void _showSaveComparisonDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'Save Comparison',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          content: const Text(
            'Save this comparison to access it later from your saved comparisons.',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Color(0xFF637488)),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Comparison saved successfully!'),
                    backgroundColor: Color(0xFFe92933),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
              ),
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  void _showAddProductDialog(int slotIndex) {
    showDialog(
      context: context,
      builder: (context) => EditProductDialog(
        currentCompany: 'SecureHealth', // Default company
        currentProduct: 'Health Insurance', // Default product
        currentPlan: 'Basic Plan', // Default plan
        currentSumInsured: '₹5,00,000', // Default sum insured
        insuranceData: _getInsuranceData(),
        onUpdate: (company, productName, plan, sumInsured) {
          setState(() {
            // Find the first available slot (leftmost null position)
            final firstEmptySlot = _comparisonSlots.indexWhere((slot) => slot == null);
            final targetIndex = firstEmptySlot != -1 ? firstEmptySlot : slotIndex;

            _comparisonSlots[targetIndex] = {
              'company': company,
              'product': productName,
              'plan': plan,
              'sumInsured': sumInsured,
              'premium': '₹15,000', // Default premium - in real app would be calculated
              'features': [
                'Hospitalization Coverage',
                'Outpatient Care',
                'Prescription Drugs',
                'Emergency Care',
              ],
              'rating': 4.0, // Default rating
              'color': const Color(0xFF086788), // Default color
            };
            _reorganizeSlots(); // Ensure proper positioning
          });
        },
      ),
    );
  }
}

// Custom painter for dotted border
class DottedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double gapLength;

  DottedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.gapLength,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(strokeWidth / 2, strokeWidth / 2,
                     size.width - strokeWidth, size.height - strokeWidth),
        const Radius.circular(12),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final segment = pathMetric.extractPath(
          distance,
          distance + dashLength,
        );
        canvas.drawPath(segment, paint);
        distance += dashLength + gapLength;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
